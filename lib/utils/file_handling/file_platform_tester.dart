import 'dart:io';

import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/utils/get_path/get_cache_dir.dart';
import 'package:dasso_reader/utils/get_path/get_temp_dir.dart';
import 'package:dasso_reader/utils/get_path/get_download_path.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

/// File handling platform testing utility for DassoShu Reader
/// 
/// This utility ensures file access patterns and path handling work
/// consistently across iOS and Android platforms, identifying any
/// platform-specific file system issues.
class FilePlatformTester {
  // Private constructor to prevent instantiation
  FilePlatformTester._();

  /// Test file handling functionality across platforms
  static Future<FileTestResult> testFileHandling() async {
    AnxLog.info('📁 File: Starting cross-platform file handling test');
    
    final testResult = FileTestResult();
    
    try {
      // Test 1: Platform detection and basic paths
      await _testPlatformPaths(testResult);
      
      // Test 2: Directory creation and access
      await _testDirectoryOperations(testResult);
      
      // Test 3: File operations (create, read, write, delete)
      await _testFileOperations(testResult);
      
      // Test 4: Path separator consistency
      await _testPathSeparators(testResult);
      
      // Test 5: Permission handling
      await _testPermissions(testResult);
      
      AnxLog.info('📁 File: Cross-platform file handling test completed');
      return testResult;
    } catch (e) {
      AnxLog.severe('📁 File: Test failed with error: $e');
      testResult.addError('General file test failure: $e');
      return testResult;
    }
  }

  /// Test platform-specific path configurations
  static Future<void> _testPlatformPaths(FileTestResult result) async {
    try {
      AnxLog.info('📁 File: Testing platform-specific paths...');
      
      final platform = defaultTargetPlatform;
      result.platform = platform.name;
      
      // Test document path
      final documentsPath = await getAnxDocumentsPath();
      result.documentsPath = documentsPath;
      
      // Test cache path
      final cacheDir = await getAnxCacheDir();
      result.cachePath = cacheDir.path;
      
      // Test temp path
      final tempDir = await getAnxTempDir();
      result.tempPath = tempDir.path;
      
      // Test download path
      try {
        final downloadPath = await getDownloadPath();
        result.downloadPath = downloadPath;
      } catch (e) {
        result.addWarning('Download path not accessible: $e');
      }
      
      // Verify platform-specific path differences
      if (platform == TargetPlatform.iOS) {
        // iOS should use Application Support directory
        if (documentsPath.contains('Application Support')) {
          result.addSuccess('iOS correctly uses Application Support directory');
        } else {
          result.addWarning('iOS path may not be using Application Support: $documentsPath');
        }
      } else if (platform == TargetPlatform.android) {
        // Android should use app_flutter directory
        if (documentsPath.contains('app_flutter')) {
          result.addSuccess('Android correctly uses app_flutter directory');
        } else {
          result.addWarning('Android path may not be using app_flutter: $documentsPath');
        }
      }
      
      result.addSuccess('Platform paths configured successfully');
    } catch (e) {
      result.addError('Platform path test failed: $e');
    }
  }

  /// Test directory creation and access operations
  static Future<void> _testDirectoryOperations(FileTestResult result) async {
    try {
      AnxLog.info('📁 File: Testing directory operations...');
      
      // Test creating app directories
      final fileDir = getFileDir();
      final coverDir = getCoverDir();
      final fontDir = getFontDir();
      
      // Test directory creation
      if (!fileDir.existsSync()) {
        fileDir.createSync(recursive: true);
      }
      if (!coverDir.existsSync()) {
        coverDir.createSync(recursive: true);
      }
      if (!fontDir.existsSync()) {
        fontDir.createSync(recursive: true);
      }
      
      // Verify directories exist
      if (fileDir.existsSync()) {
        result.addSuccess('File directory created successfully');
      } else {
        result.addError('Failed to create file directory');
      }
      
      if (coverDir.existsSync()) {
        result.addSuccess('Cover directory created successfully');
      } else {
        result.addError('Failed to create cover directory');
      }
      
      if (fontDir.existsSync()) {
        result.addSuccess('Font directory created successfully');
      } else {
        result.addError('Failed to create font directory');
      }
      
      // Test directory permissions
      await _testDirectoryPermissions(fileDir, result);
      
    } catch (e) {
      result.addError('Directory operations test failed: $e');
    }
  }

  /// Test file operations (create, read, write, delete)
  static Future<void> _testFileOperations(FileTestResult result) async {
    try {
      AnxLog.info('📁 File: Testing file operations...');
      
      final testDir = getFileDir();
      final testFile = File('${testDir.path}${Platform.pathSeparator}test_file.txt');
      
      // Test file creation
      const testContent = 'DassoShu Reader file test - 中文测试内容';
      await testFile.writeAsString(testContent);
      
      if (testFile.existsSync()) {
        result.addSuccess('File creation successful');
      } else {
        result.addError('File creation failed');
        return;
      }
      
      // Test file reading
      final readContent = await testFile.readAsString();
      if (readContent == testContent) {
        result.addSuccess('File reading successful (including Chinese characters)');
      } else {
        result.addError('File reading failed or content mismatch');
      }
      
      // Test file modification
      const modifiedContent = '$testContent\nModified content';
      await testFile.writeAsString(modifiedContent);
      
      final modifiedReadContent = await testFile.readAsString();
      if (modifiedReadContent == modifiedContent) {
        result.addSuccess('File modification successful');
      } else {
        result.addError('File modification failed');
      }
      
      // Test file deletion
      await testFile.delete();
      if (!testFile.existsSync()) {
        result.addSuccess('File deletion successful');
      } else {
        result.addError('File deletion failed');
      }
      
    } catch (e) {
      result.addError('File operations test failed: $e');
    }
  }

  /// Test path separator consistency
  static Future<void> _testPathSeparators(FileTestResult result) async {
    try {
      AnxLog.info('📁 File: Testing path separator consistency...');
      
      final expectedSeparator = Platform.pathSeparator;
      final testPath = getBasePath('test/path/structure');
      
      if (testPath.contains(expectedSeparator)) {
        result.addSuccess('Path separators are consistent with platform');
      } else {
        result.addWarning('Path separators may not be platform-consistent');
      }
      
      // Test path normalization
      final normalizedPath = testPath.replaceAll('/', Platform.pathSeparator);
      if (normalizedPath == testPath) {
        result.addSuccess('Path normalization working correctly');
      } else {
        result.addWarning('Path normalization may have issues');
      }
      
    } catch (e) {
      result.addError('Path separator test failed: $e');
    }
  }

  /// Test directory permissions
  static Future<void> _testDirectoryPermissions(Directory dir, FileTestResult result) async {
    try {
      // Test read permission
      final contents = dir.listSync();
      result.addSuccess('Directory read permission verified');
      
      // Test write permission by creating a test file
      final testFile = File('${dir.path}${Platform.pathSeparator}permission_test.tmp');
      await testFile.writeAsString('permission test');
      
      if (testFile.existsSync()) {
        result.addSuccess('Directory write permission verified');
        await testFile.delete(); // Clean up
      } else {
        result.addError('Directory write permission failed');
      }
      
    } catch (e) {
      result.addWarning('Directory permission test failed: $e');
    }
  }

  /// Test platform-specific permissions
  static Future<void> _testPermissions(FileTestResult result) async {
    try {
      AnxLog.info('📁 File: Testing platform-specific permissions...');
      
      if (defaultTargetPlatform == TargetPlatform.android) {
        // Android-specific permission tests
        result.addSuccess('Android file permissions assumed available');
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        // iOS-specific permission tests
        result.addSuccess('iOS file permissions assumed available');
      }
      
    } catch (e) {
      result.addError('Permission test failed: $e');
    }
  }
}

/// File test result container
class FileTestResult {
  String platform = 'Unknown';
  String documentsPath = '';
  String cachePath = '';
  String tempPath = '';
  String downloadPath = '';
  final List<String> successes = [];
  final List<String> warnings = [];
  final List<String> errors = [];

  void addSuccess(String message) {
    successes.add(message);
    AnxLog.info('✅ File Test: $message');
  }

  void addWarning(String message) {
    warnings.add(message);
    AnxLog.warning('⚠️ File Test: $message');
  }

  void addError(String message) {
    errors.add(message);
    AnxLog.severe('❌ File Test: $message');
  }

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get isSuccessful => errors.isEmpty;

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('File Test Result for $platform:');
    buffer.writeln('Documents Path: $documentsPath');
    buffer.writeln('Cache Path: $cachePath');
    buffer.writeln('Temp Path: $tempPath');
    buffer.writeln('Download Path: $downloadPath');
    buffer.writeln('Successes: ${successes.length}');
    buffer.writeln('Warnings: ${warnings.length}');
    buffer.writeln('Errors: ${errors.length}');
    
    if (errors.isNotEmpty) {
      buffer.writeln('\nErrors:');
      for (final error in errors) {
        buffer.writeln('  - $error');
      }
    }
    
    if (warnings.isNotEmpty) {
      buffer.writeln('\nWarnings:');
      for (final warning in warnings) {
        buffer.writeln('  - $warning');
      }
    }
    
    return buffer.toString();
  }
}
