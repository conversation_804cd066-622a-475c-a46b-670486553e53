import 'dart:io';

import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:flutter/services.dart';

Future<void> loadDefaultFont() async {
  final fontDir = getFontDir();

  // Ensure font directory exists
  if (!fontDir.existsSync()) {
    fontDir.createSync(recursive: true);
  }

  // Load NotoSansSC Regular font
  final notoSansSCRegular =
      await rootBundle.load('assets/fonts/NotoSansSC-Regular.ttf');
  final regularFontFile = File('${fontDir.path}/NotoSansSC-Regular.ttf');
  if (!regularFontFile.existsSync()) {
    regularFontFile.writeAsBytesSync(notoSansSCRegular.buffer.asUint8List());
  }

  // Load NotoSansSC Bold font
  final notoSansSCBold =
      await rootBundle.load('assets/fonts/NotoSansSC-Bold.ttf');
  final boldFontFile = File('${fontDir.path}/NotoSansSC-Bold.ttf');
  if (!boldFontFile.existsSync()) {
    boldFontFile.writeAsBytesSync(notoSansSCBold.buffer.asUint8List());
  }
}
